import React from "react";
import { ThirdwebChatInterface } from "@/components/chat/ThirdwebChatInterface";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { CheckCircle, Zap, Shield, Code } from "lucide-react";

export function ThirdwebChatDemo() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 p-4">
      <div className="max-w-6xl mx-auto space-y-8">
        {/* Header */}
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white">
            Thirdweb Nebula Chat Demo
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
            Experience the power of thirdweb's SDK with built-in transaction handling, 
            automatic UI components, and seamless blockchain interactions.
          </p>
          <div className="flex justify-center gap-2">
            <Badge variant="secondary">Thirdweb SDK v5</Badge>
            <Badge variant="secondary">Nebula AI</Badge>
            <Badge variant="secondary">Auto Transaction UI</Badge>
          </div>
        </div>

        {/* Benefits Section */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
          <Card>
            <CardContent className="p-4 text-center">
              <Zap className="h-8 w-8 text-blue-500 mx-auto mb-2" />
              <h3 className="font-semibold mb-1">Built-in Components</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                TransactionButton, PayEmbed automatically handle UI
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4 text-center">
              <Shield className="h-8 w-8 text-green-500 mx-auto mb-2" />
              <h3 className="font-semibold mb-1">Prepared Transactions</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Ready-to-execute transaction objects from Nebula
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4 text-center">
              <Code className="h-8 w-8 text-purple-500 mx-auto mb-2" />
              <h3 className="font-semibold mb-1">Less Custom Code</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                No need for custom transaction boxes or handling
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4 text-center">
              <CheckCircle className="h-8 w-8 text-orange-500 mx-auto mb-2" />
              <h3 className="font-semibold mb-1">Auto Detection</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Smart transaction type detection and labeling
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Main Chat Interface */}
        <ThirdwebChatInterface />

        {/* Features Comparison */}
        <div className="grid md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-red-600">❌ Custom Implementation</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2 text-sm">
              <p>• Custom transaction box components</p>
              <p>• Manual transaction parsing</p>
              <p>• Custom error handling</p>
              <p>• Manual gas estimation</p>
              <p>• Custom approval flows</p>
              <p>• Manual transaction status tracking</p>
              <p>• Custom swap/bridge UI</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-green-600">✅ Thirdweb SDK</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2 text-sm">
              <p>• Built-in TransactionButton component</p>
              <p>• Automatic PreparedTransaction objects</p>
              <p>• Built-in error handling & toasts</p>
              <p>• Automatic gas estimation</p>
              <p>• Built-in approval handling</p>
              <p>• Automatic transaction tracking</p>
              <p>• PayEmbed for fiat purchases</p>
            </CardContent>
          </Card>
        </div>

        {/* Code Example */}
        <Card>
          <CardHeader>
            <CardTitle>Implementation Comparison</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-semibold mb-2 text-red-600">Before (Custom)</h4>
                <pre className="text-xs bg-gray-100 dark:bg-gray-800 p-3 rounded overflow-x-auto">
{`// Custom transaction handling
const handleTransaction = async () => {
  try {
    setLoading(true);
    const tx = await prepareTransaction();
    const result = await sendTransaction(tx);
    await waitForReceipt(result.hash);
    showSuccessToast();
  } catch (error) {
    showErrorToast(error);
  } finally {
    setLoading(false);
  }
};

// Custom UI components
<CustomTransactionBox>
  <CustomButton onClick={handleTransaction}>
    {loading ? "Sending..." : "Send"}
  </CustomButton>
</CustomTransactionBox>`}
                </pre>
              </div>
              
              <div>
                <h4 className="font-semibold mb-2 text-green-600">After (Thirdweb)</h4>
                <pre className="text-xs bg-gray-100 dark:bg-gray-800 p-3 rounded overflow-x-auto">
{`// Thirdweb handles everything
const response = await nebulaChat({
  client,
  message: "Send 0.1 ETH to 0x...",
  account,
  contextFilter,
});

// Auto-generated UI
<TransactionButton
  transaction={response.transactions[0]}
  onTransactionConfirmed={(receipt) => {
    // Auto success handling
  }}
>
  Send Tokens
</TransactionButton>`}
                </pre>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Footer */}
        <div className="text-center text-sm text-gray-600 dark:text-gray-400">
          <p>
            This demo showcases how thirdweb's SDK reduces development complexity 
            while providing a better user experience.
          </p>
        </div>
      </div>
    </div>
  );
}
