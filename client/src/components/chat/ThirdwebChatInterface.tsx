import React, { useState, useRef, useEffect } from "react";
import { useThirdwebNebula } from "@/hooks/use-thirdweb-nebula";
import { ThirdwebChatMessage } from "@/components/chat/ThirdwebChatMessage";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Loader2, Send, Trash2 } from "lucide-react";
import { useActiveAccount } from "thirdweb/react";

export function ThirdwebChatInterface() {
  const account = useActiveAccount();
  const { messages, isLoading, sendMessage, clearMessages } = useThirdwebNebula();
  const [inputValue, setInputValue] = useState("");
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight;
    }
  }, [messages]);

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading) return;

    const message = inputValue.trim();
    setInputValue("");
    await sendMessage(message);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  if (!account) {
    return (
      <Card className="w-full max-w-4xl mx-auto">
        <CardContent className="p-8 text-center">
          <h3 className="text-lg font-semibold mb-2">Connect Your Wallet</h3>
          <p className="text-gray-600 dark:text-gray-400">
            Please connect your wallet to start using the Nebula chat interface.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-4xl mx-auto h-[600px] flex flex-col">
      <CardHeader className="flex-shrink-0 border-b">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <span>Nebula Chat</span>
            <span className="text-sm font-normal text-gray-500">
              Powered by Thirdweb SDK
            </span>
          </CardTitle>
          <Button
            variant="outline"
            size="sm"
            onClick={clearMessages}
            disabled={messages.length === 0}
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Clear
          </Button>
        </div>
      </CardHeader>

      <CardContent className="flex-1 flex flex-col p-0">
        {/* Messages Area */}
        <ScrollArea className="flex-1 p-4" ref={scrollAreaRef}>
          {messages.length === 0 ? (
            <div className="text-center text-gray-500 mt-8">
              <h3 className="text-lg font-medium mb-2">Welcome to Nebula Chat!</h3>
              <p className="text-sm">
                Ask me to help you with token transfers, swaps, bridges, or any blockchain operations.
              </p>
              <div className="mt-4 text-xs space-y-1">
                <p>Try asking:</p>
                <p>"Send 0.1 ETH to 0x..."</p>
                <p>"Swap 100 USDC to USDT"</p>
                <p>"Bridge USDC from Ethereum to Polygon"</p>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              {messages.map((message) => (
                <ThirdwebChatMessage
                  key={message.id}
                  message={message.content}
                  transactions={message.transactions}
                  role={message.role}
                  timestamp={message.timestamp.toISOString()}
                />
              ))}
              {isLoading && (
                <div className="flex justify-start">
                  <div className="bg-gray-100 dark:bg-gray-800 rounded-lg p-4 max-w-[80%]">
                    <div className="flex items-center gap-2">
                      <Loader2 className="h-4 w-4 animate-spin" />
                      <span className="text-sm">Nebula is thinking...</span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
        </ScrollArea>

        {/* Input Area */}
        <div className="border-t p-4 flex-shrink-0">
          <div className="flex gap-2">
            <Input
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Ask Nebula to help with blockchain operations..."
              disabled={isLoading}
              className="flex-1"
            />
            <Button
              onClick={handleSendMessage}
              disabled={!inputValue.trim() || isLoading}
              size="icon"
            >
              {isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Send className="h-4 w-4" />
              )}
            </Button>
          </div>
          <div className="text-xs text-gray-500 mt-2">
            Connected: {account.address.slice(0, 6)}...{account.address.slice(-4)}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
