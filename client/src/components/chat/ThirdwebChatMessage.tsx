import React from "react";
import { useActiveAccount } from "thirdweb/react";
import { TransactionButton } from "thirdweb/react";
import { PayEmbed } from "thirdweb/react";
import { PreparedTransaction } from "thirdweb";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { ExternalLink, Copy, Check } from "lucide-react";
import { useState } from "react";
import { toast } from "@/hooks/use-toast";

interface ThirdwebChatMessageProps {
  message: string;
  transactions?: PreparedTransaction[];
  role: "user" | "assistant";
  timestamp?: string;
}

export function ThirdwebChatMessage({
  message,
  transactions = [],
  role,
  timestamp,
}: ThirdwebChatMessageProps) {
  const account = useActiveAccount();
  const [copiedTx, setCopiedTx] = useState<string | null>(null);

  const copyToClipboard = async (text: string, txId: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedTx(txId);
      setTimeout(() => setCopiedTx(null), 2000);
      toast({
        title: "Copied to clipboard",
        description: "Transaction data copied successfully",
      });
    } catch (err) {
      toast({
        title: "Failed to copy",
        description: "Could not copy to clipboard",
        variant: "destructive",
      });
    }
  };

  const getTransactionType = (tx: PreparedTransaction): string => {
    // Analyze transaction to determine type
    if (tx.value && BigInt(tx.value) > 0n) {
      return "Token Transfer";
    }
    if (tx.data && tx.data !== "0x") {
      // Check for common function signatures
      const methodId = tx.data.slice(0, 10);
      switch (methodId) {
        case "0xa9059cbb": // transfer(address,uint256)
          return "Token Transfer";
        case "0x095ea7b3": // approve(address,uint256)
          return "Token Approval";
        case "0x38ed1739": // swapExactTokensForTokens
        case "0x7ff36ab5": // swapExactETHForTokens
          return "Token Swap";
        default:
          return "Contract Interaction";
      }
    }
    return "Transaction";
  };

  const getTransactionLabel = (tx: PreparedTransaction): string => {
    const type = getTransactionType(tx);
    switch (type) {
      case "Token Transfer":
        return "Send Tokens";
      case "Token Approval":
        return "Approve Tokens";
      case "Token Swap":
        return "Swap Tokens";
      case "Contract Interaction":
        return "Execute Contract";
      default:
        return "Execute Transaction";
    }
  };

  return (
    <div className={`flex ${role === "user" ? "justify-end" : "justify-start"} mb-4`}>
      <div
        className={`max-w-[80%] rounded-lg p-4 ${
          role === "user"
            ? "bg-blue-600 text-white"
            : "bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100"
        }`}
      >
        {/* Message Content */}
        <div className="whitespace-pre-wrap mb-3">{message}</div>

        {/* Timestamp */}
        {timestamp && (
          <div className="text-xs opacity-70 mb-3">
            {new Date(timestamp).toLocaleTimeString()}
          </div>
        )}

        {/* Transaction Components - Only for assistant messages with transactions */}
        {role === "assistant" && transactions.length > 0 && (
          <div className="space-y-3 mt-4">
            <Separator className="my-3" />
            <div className="text-sm font-medium mb-2">
              Ready to Execute ({transactions.length} transaction{transactions.length > 1 ? "s" : ""})
            </div>

            {transactions.map((transaction, index) => {
              const txType = getTransactionType(transaction);
              const txLabel = getTransactionLabel(transaction);
              const txId = `tx-${index}`;

              return (
                <Card key={index} className="border border-gray-200 dark:border-gray-700">
                  <CardContent className="p-4">
                    {/* Transaction Header */}
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-2">
                        <Badge variant="outline">{txType}</Badge>
                        <span className="text-sm font-medium">{txLabel}</span>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => copyToClipboard(JSON.stringify(transaction, null, 2), txId)}
                      >
                        {copiedTx === txId ? (
                          <Check className="h-4 w-4" />
                        ) : (
                          <Copy className="h-4 w-4" />
                        )}
                      </Button>
                    </div>

                    {/* Transaction Details */}
                    <div className="space-y-2 text-sm mb-4">
                      {transaction.to && (
                        <div className="flex justify-between">
                          <span className="text-gray-600 dark:text-gray-400">To:</span>
                          <span className="font-mono text-xs">{transaction.to}</span>
                        </div>
                      )}
                      {transaction.value && BigInt(transaction.value) > 0n && (
                        <div className="flex justify-between">
                          <span className="text-gray-600 dark:text-gray-400">Value:</span>
                          <span>{(Number(transaction.value) / 1e18).toFixed(6)} ETH</span>
                        </div>
                      )}
                      {transaction.gas && (
                        <div className="flex justify-between">
                          <span className="text-gray-600 dark:text-gray-400">Gas Limit:</span>
                          <span>{transaction.gas.toString()}</span>
                        </div>
                      )}
                    </div>

                    {/* Thirdweb Transaction Button */}
                    <div className="flex gap-2">
                      <TransactionButton
                        transaction={transaction}
                        onTransactionSent={(result) => {
                          toast({
                            title: "Transaction Sent",
                            description: `Transaction hash: ${result.transactionHash}`,
                          });
                        }}
                        onTransactionConfirmed={(receipt) => {
                          toast({
                            title: "Transaction Confirmed",
                            description: `Transaction confirmed in block ${receipt.blockNumber}`,
                          });
                        }}
                        onError={(error) => {
                          toast({
                            title: "Transaction Failed",
                            description: error.message,
                            variant: "destructive",
                          });
                        }}
                        className="flex-1"
                      >
                        {txLabel}
                      </TransactionButton>

                      {/* View on Explorer Button */}
                      {transaction.chain && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            // Open block explorer (this would need chain-specific logic)
                            const explorerUrl = `https://etherscan.io/tx/`; // Placeholder
                            window.open(explorerUrl, "_blank");
                          }}
                        >
                          <ExternalLink className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </CardContent>
                </Card>
              );
            })}

            {/* PayEmbed for Buy/Sell operations */}
            {transactions.some(tx => getTransactionType(tx) === "Token Swap") && (
              <div className="mt-4">
                <div className="text-sm font-medium mb-2">Need tokens? Buy with fiat:</div>
                <PayEmbed
                  client={require("@/lib/thirdweb").client}
                  className="w-full"
                />
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
