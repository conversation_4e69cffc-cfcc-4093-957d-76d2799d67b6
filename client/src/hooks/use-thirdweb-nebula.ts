import { useState, useCallback } from "react";
import { useActiveAccount, useActiveWallet<PERSON>hain } from "thirdweb/react";
import { chat as nebulaChat } from "thirdweb/ai";
import { client } from "@/lib/thirdweb";
import { PreparedTransaction } from "thirdweb";
import { toast } from "@/hooks/use-toast";

export interface ThirdwebChatMessage {
  id: string;
  role: "user" | "assistant";
  content: string;
  timestamp: Date;
  transactions?: PreparedTransaction[];
  sessionId?: string;
}

export interface ThirdwebChatState {
  messages: ThirdwebChatMessage[];
  isLoading: boolean;
  sessionId?: string;
}

export function useThirdwebNebula() {
  const account = useActiveAccount();
  const activeChain = useActiveWalletChain();
  
  const [state, setState] = useState<ThirdwebChatState>({
    messages: [],
    isLoading: false,
  });

  const sendMessage = useCallback(
    async (content: string) => {
      if (!account) {
        toast({
          title: "Wallet Required",
          description: "Please connect your wallet to use the chat",
          variant: "destructive",
        });
        return;
      }

      // Add user message immediately
      const userMessage: ThirdwebChatMessage = {
        id: `user-${Date.now()}`,
        role: "user",
        content,
        timestamp: new Date(),
      };

      setState(prev => ({
        ...prev,
        messages: [...prev.messages, userMessage],
        isLoading: true,
      }));

      try {
        // Prepare context filter
        const contextFilter = {
          chains: activeChain ? [activeChain] : undefined,
          walletAddresses: [account.address],
        };

        // Use thirdweb's Nebula chat
        const response = await nebulaChat({
          client,
          message: content,
          account,
          contextFilter,
          sessionId: state.sessionId,
        });

        // Add assistant message with transactions
        const assistantMessage: ThirdwebChatMessage = {
          id: `assistant-${Date.now()}`,
          role: "assistant",
          content: response.message,
          timestamp: new Date(),
          transactions: response.transactions, // These are PreparedTransaction objects
          sessionId: response.sessionId,
        };

        setState(prev => ({
          ...prev,
          messages: [...prev.messages, assistantMessage],
          isLoading: false,
          sessionId: response.sessionId,
        }));

        return assistantMessage;
      } catch (error) {
        console.error("Error sending message:", error);
        
        // Add error message
        const errorMessage: ThirdwebChatMessage = {
          id: `error-${Date.now()}`,
          role: "assistant",
          content: "Sorry, I encountered an error processing your request. Please try again.",
          timestamp: new Date(),
        };

        setState(prev => ({
          ...prev,
          messages: [...prev.messages, errorMessage],
          isLoading: false,
        }));

        toast({
          title: "Error",
          description: "Failed to send message. Please try again.",
          variant: "destructive",
        });
      }
    },
    [account, activeChain, state.sessionId]
  );

  const clearMessages = useCallback(() => {
    setState({
      messages: [],
      isLoading: false,
    });
  }, []);

  const sendMultiMessage = useCallback(
    async (messages: Array<{ role: "user" | "assistant"; content: string }>) => {
      if (!account) {
        toast({
          title: "Wallet Required",
          description: "Please connect your wallet to use the chat",
          variant: "destructive",
        });
        return;
      }

      setState(prev => ({ ...prev, isLoading: true }));

      try {
        // Prepare context filter
        const contextFilter = {
          chains: activeChain ? [activeChain] : undefined,
          walletAddresses: [account.address],
        };

        // Use thirdweb's Nebula chat with message history
        const response = await nebulaChat({
          client,
          messages,
          account,
          contextFilter,
          sessionId: state.sessionId,
        });

        // Convert messages to our format and add the response
        const convertedMessages: ThirdwebChatMessage[] = messages.map((msg, index) => ({
          id: `${msg.role}-${Date.now()}-${index}`,
          role: msg.role,
          content: msg.content,
          timestamp: new Date(),
        }));

        const assistantMessage: ThirdwebChatMessage = {
          id: `assistant-${Date.now()}`,
          role: "assistant",
          content: response.message,
          timestamp: new Date(),
          transactions: response.transactions,
          sessionId: response.sessionId,
        };

        setState(prev => ({
          ...prev,
          messages: [...prev.messages, ...convertedMessages, assistantMessage],
          isLoading: false,
          sessionId: response.sessionId,
        }));

        return assistantMessage;
      } catch (error) {
        console.error("Error sending multi-message:", error);
        
        setState(prev => ({
          ...prev,
          isLoading: false,
        }));

        toast({
          title: "Error",
          description: "Failed to process messages. Please try again.",
          variant: "destructive",
        });
      }
    },
    [account, activeChain, state.sessionId]
  );

  return {
    messages: state.messages,
    isLoading: state.isLoading,
    sessionId: state.sessionId,
    sendMessage,
    sendMultiMessage,
    clearMessages,
  };
}
